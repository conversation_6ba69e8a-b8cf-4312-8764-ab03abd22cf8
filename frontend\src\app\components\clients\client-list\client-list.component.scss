.client-list-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  
  .header-card {
    margin-bottom: 20px;
    
    mat-card-header {
      mat-card-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 1.5rem;
        
        mat-icon {
          font-size: 1.5rem;
          width: 1.5rem;
          height: 1.5rem;
        }
      }
    }

    // Enhanced filters section
    .filters-section {
      margin-bottom: 20px;

      .basic-filters {
        .filter-row {
          display: flex;
          gap: 16px;
          align-items: center;
          margin-bottom: 16px;

          .search-field {
            flex: 2;
            min-width: 300px;
          }

          .status-filter,
          .sort-field,
          .sort-order-field {
            min-width: 140px;
          }
        }
      }

      .advanced-filters {
        margin-top: 16px;
        box-shadow: none;
        border: 1px solid #e0e0e0;

        .mat-expansion-panel-header {
          padding: 16px;

          mat-panel-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
          }
        }

        .advanced-filter-content {
          padding: 0 16px 16px;

          .filter-group {
            margin-bottom: 24px;

            h4 {
              margin: 0 0 12px 0;
              font-size: 14px;
              font-weight: 500;
              color: #666;
              text-transform: uppercase;
              letter-spacing: 0.5px;
            }

            .filter-row {
              display: flex;
              gap: 16px;
              align-items: center;
              margin-bottom: 12px;

              mat-form-field {
                flex: 1;
                min-width: 200px;
              }

              .date-field {
                max-width: 200px;
              }
            }

            .selected-industries {
              margin-top: 12px;

              mat-chip-listbox {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;

                mat-chip-option {
                  background-color: #e3f2fd;
                  color: #1976d2;
                  border: 1px solid #bbdefb;

                  mat-icon {
                    color: #1976d2;
                  }

                  &:hover {
                    background-color: #bbdefb;
                  }
                }
              }
            }
          }
        }
      }
    }

    .actions-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 16px;
      margin-bottom: 20px;

      .results-info {
        display: flex;
        align-items: center;
        gap: 16px;

        .results-count {
          color: #666;
          font-size: 14px;
          font-weight: 500;
        }
      }

      button[mat-raised-button] {
        height: 56px;
        padding: 0 24px;
        font-weight: 500;
      }

      @media (max-width: 768px) {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
      }
    }

    // Mobile responsive styles for filters
    @media (max-width: 768px) {
      .filters-section {
        .basic-filters {
          .filter-row {
            flex-direction: column;
            gap: 12px;

            mat-form-field {
              width: 100%;
              min-width: auto;
            }
          }
        }

        .advanced-filters {
          .advanced-filter-content {
            .filter-group {
              .filter-row {
                flex-direction: column;
                gap: 12px;

                mat-form-field {
                  width: 100%;
                  max-width: none;
                }
              }
            }
          }
        }
      }
    }
  }
  
  .table-card {
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 40px;
      
      mat-spinner {
        margin-bottom: 16px;
      }
    }
    
    .no-data {
      text-align: center;
      padding: 40px;
      
      mat-icon {
        font-size: 4rem;
        width: 4rem;
        height: 4rem;
        color: rgba(0, 0, 0, 0.3);
        margin-bottom: 16px;
      }
      
      h3 {
        margin: 0 0 8px 0;
        color: rgba(0, 0, 0, 0.6);
      }
      
      p {
        margin: 0 0 24px 0;
        color: rgba(0, 0, 0, 0.5);
      }
    }
    
    .table-container {
      overflow-x: auto;
      
      .clients-table {
        width: 100%;
        
        .client-name {
          display: flex;
          align-items: center;
          gap: 8px;
          
          strong {
            font-weight: 500;
          }
          
          .website-link {
            color: rgba(0, 0, 0, 0.5);
            text-decoration: none;
            
            mat-icon {
              font-size: 1rem;
              width: 1rem;
              height: 1rem;
            }
            
            &:hover {
              color: var(--mat-theme-primary, #1976d2);
            }
          }
        }
        
        .email-link,
        .phone-link {
          color: var(--mat-theme-primary, #1976d2);
          text-decoration: none;
          
          &:hover {
            text-decoration: underline;
          }
        }
        
        .action-buttons {
          display: flex;
          gap: 4px;
          
          button {
            min-width: 40px;
            width: 40px;
            height: 40px;
            
            mat-icon {
              font-size: 1.2rem;
              width: 1.2rem;
              height: 1.2rem;
            }
          }
        }
        
        // Responsive table
        @media (max-width: 768px) {
          .mat-column-phone,
          .mat-column-industry {
            display: none;
          }
        }
        
        @media (max-width: 600px) {
          .mat-column-contactPerson {
            display: none;
          }
        }
      }
    }
  }
}

// Material Design overrides
.mat-mdc-table {
  .mat-mdc-header-cell {
    font-weight: 600;
    color: rgba(0, 0, 0, 0.87);
  }
  
  .mat-mdc-cell {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  }
  
  .mat-mdc-row:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
}

.mat-mdc-chip {
  font-size: 0.75rem;
  min-height: 24px;
  
  &.mat-primary {
    background-color: #e8f5e8;
    color: #2e7d32;
  }
  
  &.mat-accent {
    background-color: #fff3e0;
    color: #f57c00;
  }
  
  &.mat-warn {
    background-color: #ffebee;
    color: #d32f2f;
  }
}
