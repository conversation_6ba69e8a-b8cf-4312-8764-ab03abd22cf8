<div class="action-dialog">
  <h2 mat-dialog-title>
    <mat-icon [style.color]="getActionColor()">{{ getActionIcon() }}</mat-icon>
    {{ data.title }}
  </h2>

  <mat-dialog-content>
    <div class="project-info">
      <h3>{{ data.projectCard.role }}</h3>
      <p *ngIf="data.projectCard.clientName">{{ data.projectCard.clientName }}</p>
      <p class="current-step">Étape: {{ data.projectCard.currentStepTitle }}</p>
    </div>

    <div class="message">
      <p>{{ data.message }}</p>
    </div>

    <form [formGroup]="actionForm">
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Notes (optionnel)</mat-label>
        <textarea 
          matInput 
          formControlName="notes"
          rows="3"
          placeholder="Ajouter des commentaires sur cette action..."></textarea>
        <mat-icon matSuffix>note</mat-icon>
      </mat-form-field>
    </form>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button 
      mat-button 
      type="button" 
      (click)="onCancel()"
      [disabled]="isSubmitting">
      Annuler
    </button>
    <button 
      mat-raised-button 
      [color]="data.confirmButtonColor" 
      (click)="onConfirm()"
      [disabled]="isSubmitting">
      <mat-icon *ngIf="isSubmitting">hourglass_empty</mat-icon>
      <mat-icon *ngIf="!isSubmitting">{{ getActionIcon() }}</mat-icon>
      {{ isSubmitting ? 'Traitement...' : data.confirmButtonText }}
    </button>
  </mat-dialog-actions>
</div>
