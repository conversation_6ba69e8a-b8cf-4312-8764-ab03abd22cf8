<div class="project-list-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">
        <mat-icon>work</mat-icon>
        Mes Projets
      </h1>
      <button mat-raised-button color="primary" routerLink="/projects/new">
        <mat-icon>add</mat-icon>
        Nouveau projet
      </button>
    </div>
  </div>

  <!-- Filters -->
  <mat-card class="filters-card">
    <mat-card-header>
      <mat-card-title>
        Filtres
        <span *ngIf="getFilterCount() > 0" class="filter-count">({{ getFilterCount() }})</span>
      </mat-card-title>
      <div class="filter-actions">
        <button mat-stroked-button type="button" (click)="clearFilters()" class="clear-filters-btn">
          <mat-icon>clear</mat-icon>
          Effacer filtres
        </button>
      </div>
    </mat-card-header>
    <mat-card-content>
      <form [formGroup]="filterForm" class="filters-form">
        <!-- Basic Filters -->
        <div class="filter-section">
          <div class="filter-row">
            <mat-form-field appearance="outline" class="filter-field search-field">
              <mat-label>Recherche</mat-label>
              <input matInput formControlName="searchQuery" placeholder="Nom du projet, client, technologies...">
              <mat-icon matPrefix>search</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline" class="filter-field">
              <mat-label>TJM minimum</mat-label>
              <input matInput type="number" formControlName="minRate" placeholder="400">
              <span matSuffix>€</span>
            </mat-form-field>

            <mat-form-field appearance="outline" class="filter-field">
              <mat-label>TJM maximum</mat-label>
              <input matInput type="number" formControlName="maxRate" placeholder="800">
              <span matSuffix>€</span>
            </mat-form-field>

            <mat-form-field appearance="outline" class="filter-field">
              <mat-label>Mode de travail</mat-label>
              <mat-select formControlName="workMode">
                <mat-option value="">Tous</mat-option>
                <mat-option *ngFor="let mode of workModeOptions" [value]="mode.value">
                  {{ mode.label }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <div class="filter-row">
            <mat-form-field appearance="outline" class="filter-field">
              <mat-label>Statut</mat-label>
              <mat-select formControlName="status">
                <mat-option value="">Tous</mat-option>
                <mat-option *ngFor="let status of statusOptions" [value]="status.value">
                  {{ status.label }}
                </mat-option>
              </mat-select>
            </mat-form-field>

            <mat-form-field appearance="outline" class="filter-field">
              <mat-label>Durée</mat-label>
              <mat-select formControlName="duration">
                <mat-option value="">Toutes</mat-option>
                <mat-option *ngFor="let duration of durationOptions" [value]="duration.value">
                  {{ duration.label }}
                </mat-option>
              </mat-select>
            </mat-form-field>

            <mat-form-field appearance="outline" class="filter-field">
              <mat-label>Trier par</mat-label>
              <mat-select formControlName="sortBy">
                <mat-option *ngFor="let sort of sortOptions" [value]="sort.value">
                  {{ sort.label }}
                </mat-option>
              </mat-select>
            </mat-form-field>

            <mat-form-field appearance="outline" class="filter-field">
              <mat-label>Ordre</mat-label>
              <mat-select formControlName="sortOrder">
                <mat-option value="asc">Croissant</mat-option>
                <mat-option value="desc">Décroissant</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>

        <!-- Advanced Filters -->
        <mat-expansion-panel [expanded]="showAdvancedFilters" class="advanced-filters">
          <mat-expansion-panel-header>
            <mat-panel-title>Filtres avancés</mat-panel-title>
          </mat-expansion-panel-header>

          <div class="advanced-filter-content">
            <!-- Date Filters -->
            <div class="filter-group">
              <h4>Dates</h4>
              <div class="filter-row">
                <mat-form-field appearance="outline" class="filter-field">
                  <mat-label>Début après le</mat-label>
                  <input matInput [matDatepicker]="startFromPicker" formControlName="startDateFrom">
                  <mat-datepicker-toggle matIconSuffix [for]="startFromPicker"></mat-datepicker-toggle>
                  <mat-datepicker #startFromPicker></mat-datepicker>
                </mat-form-field>

                <mat-form-field appearance="outline" class="filter-field">
                  <mat-label>Début avant le</mat-label>
                  <input matInput [matDatepicker]="startToPicker" formControlName="startDateTo">
                  <mat-datepicker-toggle matIconSuffix [for]="startToPicker"></mat-datepicker-toggle>
                  <mat-datepicker #startToPicker></mat-datepicker>
                </mat-form-field>

                <mat-form-field appearance="outline" class="filter-field">
                  <mat-label>Fin après le</mat-label>
                  <input matInput [matDatepicker]="endFromPicker" formControlName="endDateFrom">
                  <mat-datepicker-toggle matIconSuffix [for]="endFromPicker"></mat-datepicker-toggle>
                  <mat-datepicker #endFromPicker></mat-datepicker>
                </mat-form-field>

                <mat-form-field appearance="outline" class="filter-field">
                  <mat-label>Fin avant le</mat-label>
                  <input matInput [matDatepicker]="endToPicker" formControlName="endDateTo">
                  <mat-datepicker-toggle matIconSuffix [for]="endToPicker"></mat-datepicker-toggle>
                  <mat-datepicker #endToPicker></mat-datepicker>
                </mat-form-field>
              </div>
            </div>

            <mat-divider></mat-divider>

            <!-- Location and Client Filters -->
            <div class="filter-group">
              <h4>Localisation et Client</h4>
              <div class="filter-row">
                <mat-form-field appearance="outline" class="filter-field">
                  <mat-label>Client</mat-label>
                  <input matInput formControlName="client" placeholder="Nom du client">
                  <mat-icon matPrefix>business</mat-icon>
                </mat-form-field>

                <mat-form-field appearance="outline" class="filter-field">
                  <mat-label>Localisation</mat-label>
                  <input matInput formControlName="location" placeholder="Ville, région...">
                  <mat-icon matPrefix>location_on</mat-icon>
                </mat-form-field>
              </div>
            </div>

            <mat-divider></mat-divider>

            <!-- Technology Stack Filter -->
            <div class="filter-group">
              <h4>Technologies</h4>
              <div class="tech-stack-section">
                <div class="selected-tech-chips" *ngIf="filterForm.get('selectedTechStack')?.value?.length > 0">
                  <mat-chip-set>
                    <mat-chip *ngFor="let tech of filterForm.get('selectedTechStack')?.value"
                             (removed)="removeTechStack(tech)">
                      {{ tech }}
                      <button matChipRemove>
                        <mat-icon>cancel</mat-icon>
                      </button>
                    </mat-chip>
                  </mat-chip-set>
                </div>

                <mat-form-field appearance="outline" class="tech-select-field">
                  <mat-label>Ajouter une technologie</mat-label>
                  <mat-select (selectionChange)="addTechStack($event.value); $event.source.value = ''">
                    <mat-option *ngFor="let tech of techStackOptions"
                               [value]="tech"
                               [disabled]="filterForm.get('selectedTechStack')?.value?.includes(tech)">
                      {{ tech }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline" class="filter-field">
                  <mat-label>Recherche libre</mat-label>
                  <input matInput formControlName="techStack" placeholder="React, Angular...">
                  <mat-icon matPrefix>code</mat-icon>
                </mat-form-field>
              </div>
            </div>
          </div>
        </mat-expansion-panel>
      </form>
    </mat-card-content>
  </mat-card>

  <!-- Loading -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>Chargement des projets...</p>
  </div>

  <!-- Projects Grid -->
  <div *ngIf="!isLoading && filteredProjects.length > 0" class="projects-grid">
    <mat-card class="project-card" *ngFor="let project of filteredProjects">
      <mat-card-header>
        <mat-card-title class="project-title">
          {{ project.role }}
        </mat-card-title>
        <mat-card-subtitle class="project-client">
          {{ project.clientName }}
        </mat-card-subtitle>
        <div class="project-status">
          <mat-chip [color]="getProjectStatusColor(project)" selected>
            {{ getProjectStatusText(project) }}
          </mat-chip>
        </div>
      </mat-card-header>

      <mat-card-content>
        <div class="project-details">
          <div class="detail-item" *ngIf="project.dailyRate">
            <mat-icon>euro</mat-icon>
            <span>{{ project.dailyRate }}€/jour</span>
          </div>

          <div class="detail-item" *ngIf="project.workMode">
            <mat-icon>location_on</mat-icon>
            <span>{{ getWorkModeLabel(project.workMode) }}</span>
          </div>

          <div class="detail-item" *ngIf="project.durationInMonths">
            <mat-icon>schedule</mat-icon>
            <span>{{ project.durationInMonths }} mois</span>
          </div>

          <div class="detail-item" *ngIf="project.startDate">
            <mat-icon>event</mat-icon>
            <span>{{ project.startDate | date:'dd/MM/yyyy' }}</span>
          </div>
        </div>

        <div class="project-tech" *ngIf="project.techStack">
          <mat-icon>code</mat-icon>
          <span>{{ project.techStack }}</span>
        </div>

        <p class="project-description" *ngIf="project.description">
          {{ project.description }}
        </p>
      </mat-card-content>

      <mat-card-actions class="project-actions">
        <button mat-button [routerLink]="['/projects', project.id]">
          <mat-icon>visibility</mat-icon>
          Voir
        </button>
        <button mat-button [routerLink]="['/projects', project.id, 'edit']">
          <mat-icon>edit</mat-icon>
          Modifier
        </button>
        <button mat-button color="warn" (click)="deleteProject(project.id!)">
          <mat-icon>delete</mat-icon>
          Supprimer
        </button>
      </mat-card-actions>
    </mat-card>
  </div>

  <!-- Empty State -->
  <div *ngIf="!isLoading && filteredProjects.length === 0" class="empty-state">
    <mat-card>
      <mat-card-content>
        <div class="empty-content">
          <mat-icon class="empty-icon">work_off</mat-icon>
          <h3 *ngIf="projects.length === 0">Aucun projet trouvé</h3>
          <h3 *ngIf="projects.length > 0">Aucun projet ne correspond aux filtres</h3>
          <p *ngIf="projects.length === 0">Commencez par ajouter votre premier projet !</p>
          <p *ngIf="projects.length > 0">Essayez de modifier vos critères de recherche.</p>
          <button *ngIf="projects.length === 0" mat-raised-button color="primary" routerLink="/projects/new">
            <mat-icon>add</mat-icon>
            Ajouter un projet
          </button>
          <button *ngIf="projects.length > 0" mat-stroked-button (click)="clearFilters()">
            <mat-icon>clear</mat-icon>
            Effacer les filtres
          </button>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
