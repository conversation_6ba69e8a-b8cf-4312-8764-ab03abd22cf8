import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AuthService } from '../../services/auth/auth.service';
import { ProjectService } from '../../services/project/project.service';
import { FreelanceService } from '../../services/freelance/freelance.service';
import { User, ProjectDto, FreelanceDto } from '../../models';
import { KanbanBoardComponent } from '../kanban-board/kanban-board.component';

type ViewMode = 'overview' | 'kanban';

@Component({
    selector: 'app-dashboard',
    imports: [
        CommonModule,
        RouterModule,
        MatIconModule,
        MatButtonModule,
        MatCardModule,
        MatButtonToggleModule,
        MatTooltipModule,
        KanbanBoardComponent
    ],
    templateUrl: './dashboard.component.html',
    styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  currentUser: User | null = null;
  freelanceProfile: FreelanceDto | null = null;
  recentProjects: ProjectDto[] = [];
  viewMode: ViewMode = 'overview';
  showKanbanDebug = true; // Always show debug section
  showKanbanBoard = false; // Control kanban board visibility
  stats = {
    totalProjects: 0,
    averageDailyRate: 0,
    totalRevenue: 0,
    activeProjects: 0
  };



  constructor(
    private readonly authService: AuthService,
    private readonly projectService: ProjectService,
    private readonly freelanceService: FreelanceService,
    private readonly cdr: ChangeDetectorRef
  ) {
    console.log('Dashboard: Constructor called, initial viewMode:', this.viewMode);
  }

  ngOnInit(): void {
    console.log('Dashboard: ngOnInit called, viewMode:', this.viewMode);
    this.currentUser = this.authService.getUser();
    console.log('Dashboard: currentUser:', this.currentUser);
    this.loadDashboardData();
  }

  loadDashboardData(): void {
    if (this.currentUser?.id) {
      // Load freelance profile
      this.freelanceService.getByIdWithProjects(this.currentUser.id).subscribe({
        next: (profile) => {
          this.freelanceProfile = profile;
          this.updateStats(profile);
        },
        error: (error) => console.error('Error loading profile:', error)
      });

      // Load recent projects
      this.projectService.getByFreelanceId(this.currentUser.id).subscribe({
        next: (projects) => {
          this.recentProjects = projects.slice(0, 5); // Get 5 most recent
        },
        error: (error) => console.error('Error loading projects:', error)
      });
    }
  }

  updateStats(profile: FreelanceDto): void {
    this.stats = {
      totalProjects: profile.totalProjects || 0,
      averageDailyRate: profile.averageDailyRate || 0,
      totalRevenue: this.calculateTotalRevenue(),
      activeProjects: this.recentProjects.filter(p => p.startDate && new Date(p.startDate) <= new Date()).length
    };
  }

  calculateTotalRevenue(): number {
    return this.recentProjects.reduce((total, project) => {
      if (project.dailyRate && project.durationInMonths && project.daysPerYear) {
        const monthlyDays = project.daysPerYear / 12;
        return total + (project.dailyRate * monthlyDays * project.durationInMonths);
      }
      return total;
    }, 0);
  }

  logout(): void {
    this.authService.logout();
  }

  switchView(mode: any): void {
    console.log('Dashboard: switchView called with mode:', mode, 'type:', typeof mode);
    if (mode === 'overview' || mode === 'kanban') {
      this.viewMode = mode as ViewMode;
      console.log('Dashboard: viewMode set to:', this.viewMode);
      console.log('Dashboard: Triggering change detection...');
      this.cdr.detectChanges();
      console.log('Dashboard: Change detection triggered');
    } else {
      console.error('Dashboard: Invalid mode received:', mode);
    }
  }

  isOverviewMode(): boolean {
    const result = this.viewMode === 'overview';
    console.log('Dashboard: isOverviewMode() =', result, 'viewMode =', this.viewMode);
    return result;
  }

  isKanbanMode(): boolean {
    console.log('Dashboard: isKanbanMode() called! viewMode =', this.viewMode);
    const result = this.viewMode === 'kanban';
    console.log('Dashboard: isKanbanMode() result =', result);
    return result;
  }

  getCharCodes(str: string): string {
    return Array.from(str).map(char => char.charCodeAt(0)).join(',');
  }

  setKanbanMode(): void {
    console.log('Dashboard: setKanbanMode() called');
    console.log('Dashboard: Before - viewMode =', this.viewMode, 'showKanbanBoard =', this.showKanbanBoard);
    this.viewMode = 'kanban';
    this.showKanbanBoard = true;
    console.log('Dashboard: After - viewMode =', this.viewMode, 'showKanbanBoard =', this.showKanbanBoard);
    this.cdr.detectChanges();
    console.log('Dashboard: Change detection triggered');
  }

  setOverviewMode(): void {
    console.log('Dashboard: setOverviewMode() called');
    console.log('Dashboard: Before - viewMode =', this.viewMode, 'showKanbanBoard =', this.showKanbanBoard);
    this.viewMode = 'overview';
    this.showKanbanBoard = false;
    console.log('Dashboard: After - viewMode =', this.viewMode, 'showKanbanBoard =', this.showKanbanBoard);
    this.cdr.detectChanges();
    console.log('Dashboard: Change detection triggered');
  }

  getGreeting(): string {
    const hour = new Date().getHours();
    if (hour < 12) {
      return 'Bonjour';
    } else if (hour < 18) {
      return 'Bon après-midi';
    } else {
      return 'Bonsoir';
    }
  }
}
