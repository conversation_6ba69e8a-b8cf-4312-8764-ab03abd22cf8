.action-dialog {
  min-width: 400px;
  max-width: 500px;
}

h2[mat-dialog-title] {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
}

.project-info {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  
  h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }
  
  p {
    margin: 0 0 4px 0;
    color: #666;
    
    &.current-step {
      font-weight: 500;
      color: #2196f3;
    }
  }
}

.message {
  margin-bottom: 20px;
  
  p {
    margin: 0;
    color: #666;
    line-height: 1.5;
  }
}

.full-width {
  width: 100%;
}

mat-form-field {
  margin-bottom: 16px;
}

mat-dialog-actions {
  padding: 16px 0 0 0;
  margin: 0;
  
  button {
    margin-left: 8px;
    
    &:first-child {
      margin-left: 0;
    }
  }
}

// Responsive design
@media (max-width: 480px) {
  .action-dialog {
    min-width: 300px;
    max-width: 90vw;
  }
}
