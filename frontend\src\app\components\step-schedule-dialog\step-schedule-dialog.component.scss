.schedule-dialog {
  min-width: 400px;
  max-width: 500px;
}

h2[mat-dialog-title] {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  
  mat-icon {
    color: #2196f3;
  }
}

.project-info {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  
  h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }
  
  p {
    margin: 0 0 4px 0;
    color: #666;
    
    &.current-step {
      font-weight: 500;
      color: #2196f3;
    }
  }
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  
  .date-field {
    flex: 2;
  }
  
  .time-field {
    flex: 1;
  }
}

.full-width {
  width: 100%;
}

mat-form-field {
  margin-bottom: 16px;
}

mat-dialog-actions {
  padding: 16px 0 0 0;
  margin: 0;
  
  button {
    margin-left: 8px;
    
    &:first-child {
      margin-left: 0;
    }
  }
}

// Responsive design
@media (max-width: 480px) {
  .schedule-dialog {
    min-width: 300px;
    max-width: 90vw;
  }
  
  .form-row {
    flex-direction: column;
    gap: 0;
  }
}
