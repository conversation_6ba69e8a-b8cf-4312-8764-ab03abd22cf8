.kanban-board-container {
  padding: 20px;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.board-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h2 {
    margin: 0;
    font-size: 28px;
    font-weight: 600;
    color: #333;
  }
}

.loading-container {
  margin-bottom: 20px;
}

.kanban-board {
  display: flex;
  gap: 20px;
  overflow-x: auto;
  overflow-y: hidden;
  flex: 1;
  padding-bottom: 20px;
}

.step-column {
  min-width: 300px;
  max-width: 350px;
  background-color: #f5f5f5;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  height: fit-content;
  max-height: calc(100vh - 140px);
}

.column-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
  
  .project-count {
    background-color: #2196f3;
    color: white;
    border-radius: 12px;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
  }
}

.project-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow-y: auto;
  flex: 1;
  min-height: 100px;
}

.project-card {
  cursor: grab;
  
  &:active {
    cursor: grabbing;
  }
  
  .card-content {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
  }
}

.card-info {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
  
  > div {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
    color: #666;
    
    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }
  }
}

.tech-stack {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
  
  mat-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
  }
}

.status-info {
  margin-bottom: 8px;
  
  mat-chip {
    font-size: 12px;
    height: 24px;
    border-radius: 12px;
  }
}

.date-info {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
  
  mat-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
  }
}

.progress-info {
  margin-bottom: 8px;
  
  .progress-text {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
    
    .progress-percentage {
      font-weight: 600;
    }
  }
  
  mat-progress-bar {
    height: 6px;
    border-radius: 3px;
  }
}

.notes {
  display: flex;
  align-items: flex-start;
  gap: 4px;
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  
  mat-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
    margin-top: 2px;
  }
  
  span {
    line-height: 1.4;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}

.drag-placeholder {
  background: #ccc;
  border: dotted 3px #999;
  min-height: 60px;
  border-radius: 8px;
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.empty-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #999;
  text-align: center;
  
  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }
  
  p {
    margin: 0;
    font-size: 14px;
  }
}

.empty-board {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  
  mat-card {
    max-width: 400px;
    text-align: center;
  }
  
  .empty-content {
    padding: 40px 20px;
    
    .empty-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      color: #ccc;
      margin-bottom: 20px;
    }
    
    h3 {
      margin: 0 0 16px 0;
      color: #333;
    }
    
    p {
      margin: 0 0 24px 0;
      color: #666;
      line-height: 1.5;
    }
  }
}

.warn-action {
  color: #f44336 !important;
}

// CDK Drag and Drop styles
.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 8px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
              0 8px 10px 1px rgba(0, 0, 0, 0.14),
              0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.project-list.cdk-drop-list-dragging .project-card:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

// Responsive design
@media (max-width: 768px) {
  .kanban-board-container {
    padding: 10px;
  }
  
  .kanban-board {
    gap: 10px;
  }
  
  .step-column {
    min-width: 280px;
    max-width: 300px;
  }
  
  .board-header h2 {
    font-size: 24px;
  }
}
