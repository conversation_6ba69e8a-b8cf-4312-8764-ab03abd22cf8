.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  margin-bottom: 32px;

  .welcome-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
  }

  .welcome-text {
    flex: 1;
  }

  .view-toggle {
    mat-button-toggle-group {
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      mat-button-toggle {
        border: none;

        &.mat-button-toggle-checked {
          background-color: #2196f3;
          color: white;
        }

        mat-icon {
          margin-right: 8px;
        }
      }
    }
  }
}

.welcome-title {
  font-size: 32px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #333;
}

.welcome-subtitle {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  mat-icon {
    font-size: 24px;
    color: white;
  }
  
  &.projects {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  &.rate {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }
  
  &.revenue {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }
  
  &.active {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  }
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

.recent-projects-section {
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h2 {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
    color: #333;
  }
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.project-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
}

.project-info {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
  
  > div {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
    color: #666;
    
    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }
  }
}

.project-tech {
  font-size: 14px;
  color: #888;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.empty-state {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-content {
  text-align: center;
  padding: 40px 20px;
  
  .empty-icon {
    font-size: 64px;
    width: 64px;
    height: 64px;
    color: #ccc;
    margin-bottom: 16px;
  }
  
  h3 {
    font-size: 20px;
    font-weight: 500;
    margin: 0 0 8px 0;
    color: #333;
  }
  
  p {
    color: #666;
    margin: 0 0 24px 0;
  }
}

// View mode specific styles
.overview-content {
  // Existing styles are already applied to the content
}

.kanban-content {
  // Remove default dashboard padding for full-width kanban board
  margin: -20px;

  app-kanban-board {
    display: block;
    height: calc(100vh - 120px); // Adjust based on header height
  }
}

// Responsive design
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }
  
  .welcome-title {
    font-size: 24px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }
  
  .projects-grid {
    grid-template-columns: 1fr;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .welcome-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .view-toggle {
    width: 100%;

    mat-button-toggle-group {
      width: 100%;

      mat-button-toggle {
        flex: 1;
      }
    }
  }

  .kanban-content {
    margin: -16px;

    app-kanban-board {
      height: calc(100vh - 100px);
    }
  }
}
